import React, {useCallback, useState} from 'react';
import TemplateSelectionFilter, {
  TemplateSelectionFilterValues,
} from './components/TemplateSelectionFilters';
import {FilterValue} from '../RAListing/components/RAFilters';
import {
  MostlyUsedCard,
  MostlyUsedCardList,
  MostlyUsedCardProps,
} from '../RATemplateListing/components/MostlyUsedCard';
import CardGallery from '../../components/CardGallery';
import {useInfiniteQuery} from '../../hooks';
import {TemplateListResponse} from '../../types';
import {getTemplateList} from '../../services/services';
import {cleanObject} from '../../utils/common';
import {TemplateStatus} from '../../enums';
import {ExternalLinkIcon} from '../../components/icons';
import {useNavigate} from 'react-router-dom';
import BottomButton from '../../components/BottomButton';

import '../../styles/components/template-selection.scss';
import {PreviewTemplateModal} from '../../components/PreviewTemplateModal';

export default function TemplateSelection() {
  const navigate = useNavigate();
  const [selectedTemplate, setSelectedTemplate] = useState<
    number | undefined
  >();
  const [filters, setFilters] = useState<TemplateSelectionFilterValues>({
    search: '',
    ra_categories: null,
    hazard_categories: null,
  });
  const [showPreviewModal, setShowPreviewModal] = useState(false);

  const filtersAndSorters = {
    status: TemplateStatus.PUBLISHED,
    search: filters.search || null,
    ra_categories: filters.ra_categories || null,
    hazard_categories: filters.hazard_categories || null,
  };

  const {data, isFetchingNextPage, isLoading, fetchNextPage} = useInfiniteQuery<
    TemplateListResponse['result']['data'][0],
    TemplateListResponse['result']
  >(getTemplateList, {
    limit: 100,
    ...cleanObject(filtersAndSorters),
  });

  const handleFilterChange = useCallback(
    (key: keyof TemplateSelectionFilterValues, value: FilterValue) => {
      setFilters(prev => ({...prev, [key]: value}));
      setSelectedTemplate(undefined);
    },
    [setFilters, setSelectedTemplate],
  );

  const updatedData: (MostlyUsedCardProps & {id: number})[] = data.data.map(
    item => {
      const userName =
        data.userDetails.find(user => user.userId === item.created_by)
          ?.full_name || item.created_by;

      return {
        id: item.id,
        templateId: item.id,
        templateName: item.task_requiring_ra,
        riskCategories: item.template_category,
        hazardCategories: item.template_hazards,
        keywords: item.template_keywords,
        createdOn: item.created_at,
        userName: userName,
      };
    },
  );

  const $TemplatePreviewButton = (
    <button
      style={{all: 'unset'}}
      className="ra-drawer-close"
      aria-label="Open Templates"
      data-testid="open-templates-btn"
      onClick={() => setShowPreviewModal(true)}
    >
      <ExternalLinkIcon className="cursor-pointer" />
    </button>
  );

  return (
    <div className="ra-template-selection">
      <h3 className="breadcrumb-text">Creating Risk Assessment</h3>
      <main className="template-selection-main">
        <div className="main-title">Select a Template</div>
        <TemplateSelectionFilter
          filters={filters}
          onFilterChange={handleFilterChange}
        />
        <div className="template-selection-content w-100">
          <MostlyUsedCardList
            hideMenu
            extraFooterOptions={$TemplatePreviewButton}
            onClick={templateId => setSelectedTemplate(templateId)}
            selectedCardId={selectedTemplate}
          />

          <div className="w-100">
            <div className="all-template-text">All Templates</div>
            <CardGallery
              data={updatedData}
              renderItem={item => (
                <MostlyUsedCard
                  hideMenu
                  {...item}
                  extraFooterOptions={$TemplatePreviewButton}
                  onClick={templateId => setSelectedTemplate(templateId)}
                  isSelected={selectedTemplate === item.templateId}
                />
              )}
              isLoading={isLoading}
              isFetchingNextPage={isFetchingNextPage}
              pagination={data.pagination}
              fetchNextPage={fetchNextPage}
            />
          </div>
        </div>
      </main>
      <BottomButton
        customContainerClass="template-selection-bottom-btn"
        buttons={[
          {
            title: 'Cancel',
            testID: 'form-prj-cancel-btn',
            variant: 'secondary',
            customClass: 'sec-btn fs-14',
            onClick: () => navigate('/risk-assessment'),
          },
          {
            title: 'Use Template',
            testID: 'form-prj-save-btn',
            variant: 'primary',
            customClass: 'primary-btn fs-14',
            disabled: !selectedTemplate,
            onClick: () => {
              navigate(
                `/risk-assessment/templates/${selectedTemplate}/risks/create`,
              );
            },
          },
        ]}
      />
      {showPreviewModal && !!selectedTemplate && (
        <PreviewTemplateModal
          onClose={() => setShowPreviewModal(false)}
          id={selectedTemplate}
        />
      )}
    </div>
  );
}
