import React, {useReducer, useState} from 'react';
import {<PERSON><PERSON>, But<PERSON>} from 'react-bootstrap';
import {toast} from 'react-toastify';
import {markTemplateAsArchived} from '../../../services/services';

import '../../../styles/components/archive-template-modal.scss';
import {MostlyUsedCard, MostlyUsedCardProps} from './MostlyUsedCard';

interface ArchiveTemplateModalProps
  extends Pick<
    MostlyUsedCardProps,
    | 'templateId'
    | 'templateName'
    | 'riskCategories'
    | 'hazardCategories'
    | 'keywords'
  > {
  trigger: React.ReactElement;
  createdOn: string;
  userName: string;
  onSuccess?: () => void;
}

const ArchiveTemplateModal: React.FC<ArchiveTemplateModalProps> = props => {
  const {trigger, templateId, onSuccess, ...restProps} = props;
  const [isOpen, toggle] = useReducer(t => !t, false);
  const [isArchiving, setIsArchiving] = useState(false);

  const onArchive = async () => {
    if (isArchiving) return; // Prevent multiple simultaneous operations

    setIsArchiving(true);
    try {
      await markTemplateAsArchived(templateId);
      onSuccess?.();
      toast.success('Template archived successfully');
    } catch (error) {
      console.error('Error archiving template:', error);
      toast.error(
        error instanceof Error ? error.message : 'Failed to archive template',
      );
    } finally {
      setIsArchiving(false);
      toggle();
    }
  };

  return (
    <>
      {React.cloneElement(trigger, {onClick: toggle})}
      <Modal show={isOpen} onHide={toggle} centered>
        <div className="archive-template-modal">
          <div>
            <div className="modal-header">Archive Template</div>
          </div>
          <div className="modal-content-container">
            <div className="alert-large">
              <span className="alert-text">
                <span>
                  <strong>
                    Do you really want to Archive this template? This action is
                    not revertible.
                  </strong>
                </span>{' '}
                After archiving the template, this won’t be available for future
                use.
              </span>
            </div>
            <MostlyUsedCard
              hideMenu
              className="template-detail-card"
              templateId={templateId}
              {...restProps}
            />
          </div>
          <div className="button-group">
            <Button variant="primary" onClick={toggle} disabled={isArchiving}>
              Cancel
            </Button>
            <Button
              variant="secondary"
              onClick={onArchive}
              disabled={isArchiving}
            >
              {isArchiving ? 'Archiving...' : 'Move to Archive'}
            </Button>
          </div>
        </div>
      </Modal>
    </>
  );
};

export default ArchiveTemplateModal;
